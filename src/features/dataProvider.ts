import jsonServerProvider from 'ra-data-simple-rest'
import { fetchUtils } from 'react-admin'
import { envs } from '../common/utils/envs'
import { getIdentity } from './authentication/utils/utils'

const httpClient = (url: string, options?: fetchUtils.Options) => {
  if (options) {
    options.headers = new Headers(options.headers || {})
    options.headers.set(
      'X-Auth-Token',
      getIdentity()?.auth_token_info.authToken ?? ''
    )
    // Needed because backend...
    options.headers.set('Content-Type', 'application/json')
  }

  return fetchUtils.fetchJson(decodeURIComponent(url), options)
}

// Create a custom data provider
const dataProvider = jsonServerProvider(envs.baseUrl, httpClient)

export { dataProvider }
