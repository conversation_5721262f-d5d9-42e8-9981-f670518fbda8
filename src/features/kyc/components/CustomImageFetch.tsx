import { Box, CircularProgress, Stack, Typography } from '@mui/material'
import { useGetOne } from 'react-admin'
import { en } from '../../../i18n/en'
import { CustomImageFetchProps } from '../types/CustomImageFetch.types'

type RenderImageParams = {
  imageData: string
  mimeType: string
  imageAlt: string
  width: number
  height: number
}

const renderImage = ({
  imageData,
  mimeType,
  imageAlt,
  width,
  height,
}: RenderImageParams) => {
  if (mimeType === 'application/pdf') {
    return (
      <embed
        src={`data:${mimeType};base64, ${imageData}`}
        width={width}
        height={height}
        type='application/pdf'
      />
    )
  }

  return (
    <img
      src={`data:${mimeType};base64, ${imageData}`}
      alt={imageAlt}
      width={width}
      height={height}
      style={{ objectFit: 'contain' }}
    />
  )
}

/**
 * Component for fetching and displaying images from the API
 * Supports single document, front_image and back_image formats
 */
export const CustomImageFetch = ({
  id,
  resource,
  alt = 'Fetched image',
  width = 1000,
  height = 500,
}: CustomImageFetchProps) => {
  const { data, isLoading, error } = useGetOne(
    resource,
    {
      id,
    },
    {
      retry: 2,
      staleTime: 30000,
    }
  )

  if (isLoading) {
    return (
      <Box
        display='flex'
        justifyContent='center'
        alignItems='center'
        width={width}
        height={height}
      >
        <CircularProgress />
      </Box>
    )
  }

  if (error || (!data?.document && !data?.front_image)) {
    return (
      <Box
        display='flex'
        justifyContent='center'
        alignItems='center'
        width={width}
        height={height}
      >
        <Typography color='error'>{en.common.error.imageError}</Typography>
      </Box>
    )
  }

  // Handle single document case
  if (data.document) {
    return renderImage({
      imageData: data.document,
      mimeType: data.document_mime_type,
      imageAlt: alt,
      width,
      height,
    })
  }

  // Handle front and back image case
  return (
    <Stack spacing={2} width={width} gap={1} alignItems={'center'}>
      <Box>
        {renderImage({
          imageData: data.front_image,
          mimeType: data.front_image_mime_type || 'image/jpeg',
          imageAlt: `${alt} - Front`,
          width,
          height,
        })}
      </Box>
      {data.back_image && (
        <Box>
          {renderImage({
            imageData: data.back_image,
            mimeType: data.back_image_mime_type || 'image/jpeg',
            imageAlt: `${alt} - Back`,
            width,
            height,
          })}
        </Box>
      )}
    </Stack>
  )
}
