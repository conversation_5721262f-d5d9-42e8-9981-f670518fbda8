import { CircularProgress } from '@mui/material'
import { useRecordContext } from 'react-admin'
import { resources } from '../resources'
import { CustomImageFetch } from './CustomImageFetch'

/**
 * Fetches the document proof image for the ID document review using `proof_id` key
 */
const IdDocumentImage = () => {
  const record = useRecordContext()

  if (!record?.id) {
    return <CircularProgress />
  }

  return (
    <CustomImageFetch
      id={`${record?.id}`}
      resource={resources.idDocumentImgs}
    />
  )
}

export { IdDocumentImage }
