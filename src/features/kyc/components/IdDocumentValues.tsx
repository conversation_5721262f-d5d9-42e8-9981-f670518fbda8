import { DateField, SimpleShowLayout, TextField } from 'react-admin'
import { en } from '../../../i18n/en'

export const IdDocumentValues = ({
  record,
}: {
  record: 'scanned_values' | 'user_confirmed_values'
}) => {
  return (
    <SimpleShowLayout direction='row' spacing={10}>
      <TextField
        source={`${record}.first_name`}
        label={en.kyc.label.firstName}
      />
      <TextField source={`${record}.last_name`} label={en.kyc.label.lastName} />
      <DateField
        source={`${record}.date_of_birth`}
        label={en.kyc.label.dateOfBirth}
      />
      <TextField source={`${record}.gender`} label={en.common.label.sex} />
      <TextField
        source={`${record}.id_number`}
        label={en.kyc.label.documentNumber}
      />
    </SimpleShowLayout>
  )
}
