import { SimpleShowLayout, TextField } from 'react-admin'
import { LocalizedDateField } from '../../../common/components/LocalizedDateField'
import { en } from '../../../i18n/en'

export const UserAddressDetails = () => {
  return (
    <SimpleShowLayout direction='row' spacing={10}>
      <TextField
        source='address_submission.user_submitted_address.country'
        label={en.kyc.label.country}
      />
      <TextField
        source='address_submission.user_submitted_address.state_province'
        label={en.kyc.label.state}
      />
      <TextField
        source='address_submission.user_submitted_address.postal_code'
        label={en.kyc.label.postalCode}
      />
      <TextField
        source='address_submission.user_submitted_address.line_1'
        label={en.kyc.label.addressLine1}
      />
      <TextField
        source='address_submission.user_submitted_address.line_2'
        label={en.kyc.label.addressLine2}
      />
      <TextField
        source='address_submission.user_submitted_address.city'
        label={en.kyc.label.city}
      />

      <LocalizedDateField
        source='address_submission.user_submitted_address.submitted_at'
        label={en.kyc.label.addressSubmissionDate}
        showTime
      />
    </SimpleShowLayout>
  )
}
