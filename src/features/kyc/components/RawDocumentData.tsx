import { SimpleShowLayout, TextField } from 'react-admin'
import { en } from '../../../i18n/en'

export const RawDocumentData = () => {
  return (
    <SimpleShowLayout direction='row' spacing={10}>
      <TextField
        source='raw_document_data.barcode_values'
        label={en.kyc.label.barcodeValues}
      />

      <TextField
        source='raw_document_data.nfc_values'
        label={en.kyc.label.nfcValues}
      />

      <TextField
        source='raw_document_data.user_confirmed_values'
        label={en.kyc.label.userConfirmedValues}
      />

      <TextField
        source='raw_document_data.template_info.template_name'
        label={en.kyc.label.templateName}
      />

      <TextField
        source='raw_document_data.template_info.template_type'
        label={en.kyc.label.templateType}
      />
    </SimpleShowLayout>
  )
}
