type RawDocumentData = {
  barcodeValues: string | null
  nfcValues: string | null
  scannedValues: string | null
  templateInfo: {
    templateName: string
    templateType: string
  }
  userConfirmedValues: string | null
}

type ScannedValues = {
  additional_names: Array<string>
  date_of_birth: string | null
  first_name: string | null
  gender: string | null
  id_number: string | null
  last_name: string | null
}

type UserConfirmedValues = {
  additional_names: Array<string>
  date_of_birth: string
  first_name: string
  gender: string
  id_number: string
  last_name: string
}

type IdDocumentData = {
  id: string
  id_type: string
  raw_document_data: RawDocumentData
  scanned_values: ScannedValues
  user_confirmed_values: UserConfirmedValues
  user_id: string
}

export type {
  IdDocumentData,
  RawDocumentData,
  ScannedValues,
  UserConfirmedValues,
}
