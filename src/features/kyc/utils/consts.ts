import { en } from '../../../i18n/en'

const addressDenyOptions = [
  {
    id: 'NAME_MISMATCH',
    name: en.kyc.label.denyNameMismatch,
  },
  {
    id: 'DOCUMENT_TOO_OLD',
    name: en.kyc.label.denyDocTooOld,
  },
  {
    id: 'ILLEGIBLE_DOCUMENT',
    name: en.kyc.label.denyIllegibleDoc,
  },
  {
    id: 'ADDRESS_NOT_SHOWN',
    name: en.kyc.label.denyDocumentNotShown,
  },
  {
    id: 'OTHER_REASON',
    name: en.kyc.label.denyOtherReason,
  },
]

const idDocumentDenyOptions = [
  {
    id: 'NAME_MISMATCH',
    name: en.kyc.label.denyNameMismatch,
  },
  {
    id: 'DOCUMENT_TOO_OLD',
    name: en.kyc.label.denyDocTooOld,
  },
  {
    id: 'ILLEGIBLE_DOCUMENT',
    name: en.kyc.label.denyIllegibleDoc,
  },
  {
    id: 'DOCUMENT_NOT_SHOWN',
    name: en.kyc.label.denyDocumentNotShown,
  },
  {
    id: 'OTHER_REASON',
    name: en.kyc.label.denyOtherReason,
  },
]

export { addressDenyOptions, idDocumentDenyOptions }
