import { Datagrid, List, SelectInput, TextField } from 'react-admin'
import { LocalizedDateField } from '../../../common/components/LocalizedDateField'
import { LocalizedDateInput } from '../../../common/components/LocalizedDateInput'
import { DocumentSubmissionStatus } from '../../../common/types/DocumentSubmissionStatus.types'
import { en } from '../../../i18n/en'

const statusColors: Record<DocumentSubmissionStatus, string> = {
  SUBMITTED: '6px solid orange',
  ANALYSIS: '6px solid orange',
  APPROVED: '6px solid green',
  DENIED: '6px solid red',
} as const

export const AddressReviewList = () => {
  return (
    <List
      perPage={25}
      queryOptions={{
        refetchInterval: 20_000,
      }}
      filters={[
        <LocalizedDateInput
          key={'GTE_submittedAt'}
          source='GTE_submittedAt'
          label={en.kyc.label.from}
          alwaysOn
        />,
        <LocalizedDateInput
          key={'LTE_submittedAt'}
          source='LTE_submittedAt'
          label={en.kyc.label.to}
          alwaysOn
        />,
        <SelectInput
          key={'status'}
          source='status'
          label={en.kyc.label.status}
          alwaysOn
          choices={[
            {
              id: 'SUBMITTED',
              name: `${en.kyc.label.submitted} 🟠`,
            },
            {
              id: 'ANALYSIS',
              name: `${en.kyc.label.analysis} 🟠`,
            },
            {
              id: 'APPROVED',
              name: `${en.kyc.label.approved} 🟢`,
            },
            {
              id: 'DENIED',
              name: `${en.kyc.label.denied} 🔴`,
            },
          ]}
        />,
      ]}
    >
      <Datagrid
        rowClick='show'
        bulkActionButtons={false}
        rowSx={(record) => ({
          borderLeft:
            statusColors[
              (record.address_submission?.status as DocumentSubmissionStatus) ??
                'SUBMITTED'
            ],
        })}
      >
        <TextField
          source='user_details.name'
          label={en.common.label.verifiedName}
        />
        <TextField source='user_details.email' label={en.common.label.email} />
        <TextField
          source='address_submission.user_submitted_address.country'
          label={en.kyc.label.country}
        />
        <TextField
          source='address_submission.user_submitted_address.state_province'
          label={en.kyc.label.state}
        />
        <TextField
          source='address_submission.user_submitted_address.postal_code'
          label={en.kyc.label.postalCode}
        />
        <TextField
          source='address_submission.user_submitted_address.line_1'
          label={en.kyc.label.addressLine1}
        />
        <TextField
          source='address_submission.user_submitted_address.line_2'
          label={en.kyc.label.addressLine2}
        />
        <TextField
          source='address_submission.user_submitted_address.city'
          label={en.kyc.label.city}
        />
        <LocalizedDateField
          source='address_submission.user_submitted_address.submitted_at'
          label={en.kyc.label.addressSubmissionDate}
          showTime
        />
      </Datagrid>
    </List>
  )
}
