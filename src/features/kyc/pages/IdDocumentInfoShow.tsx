import { Stack } from '@mui/material'
import { Show, SimpleShowLayout } from 'react-admin'
import { ApproveActionDialog } from '../../../common/components/ApproveActionDialog'
import { DenyActionDialog } from '../../../common/components/DenyActionDialog'
import { SectionHeader } from '../../../common/components/SectionHeader'
import { en } from '../../../i18n/en'
import { IdDocumentValues } from '../components/IdDocumentValues'
import { resources } from '../resources'
import { idDocumentDenyOptions } from '../utils/consts'
import { IdDocumentImage } from '../components/IdDocumentImage'
import { RawDocumentData } from '../components/RawDocumentData'

export const IdDocumentInfoShow = () => {
  return (
    <Show
      queryOptions={{
        enabled: false,
      }}
    >
      <SimpleShowLayout>
        <SectionHeader>{en.kyc.group.rawDocumentData}</SectionHeader>
        <RawDocumentData />
        <SectionHeader>{en.kyc.group.userConfirmedValues}</SectionHeader>
        <IdDocumentValues record='user_confirmed_values' />
        <SectionHeader>{en.kyc.group.scannedValues}</SectionHeader>
        <IdDocumentValues record='scanned_values' />
        <SectionHeader>{en.kyc.group.idDocument}</SectionHeader>
        <IdDocumentImage />
        <Stack direction='row' spacing={10}>
          <ApproveActionDialog
            recordKey='id'
            resource={resources.idDocumentApprove}
            redirectTo={`/${resources.idDocumentListAll}`}
            approvedActionMsg={en.common.feedback.documentApproved}
          />
          <DenyActionDialog
            recordKey='id'
            resource={resources.idDocumentDeny}
            redirectTo={`/${resources.idDocumentListAll}`}
            deniedActionMsg={en.common.feedback.documentDenied}
            denyReasons={idDocumentDenyOptions}
          />
        </Stack>
      </SimpleShowLayout>
    </Show>
  )
}
