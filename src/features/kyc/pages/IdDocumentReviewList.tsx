import {
  Datagrid,
  List,
  SearchInput,
  SelectInput,
  TextField,
} from 'react-admin'
import { LocalizedDateField } from '../../../common/components/LocalizedDateField'
import { LocalizedDateInput } from '../../../common/components/LocalizedDateInput'
import { DocumentSubmissionStatus } from '../../../common/types/DocumentSubmissionStatus.types'
import { en } from '../../../i18n/en'

const statusColors: Record<DocumentSubmissionStatus, string> = {
  SUBMITTED: '6px solid orange',
  ANALYSIS: '6px solid orange',
  APPROVED: '6px solid green',
  DENIED: '6px solid red',
} as const

export const IdDocumentReviewList = () => {
  return (
    <List
      perPage={25}
      queryOptions={{
        refetchInterval: 20_000,
      }}
      filters={[
        <SearchInput
          key={'lastName'}
          source='~lastName'
          alwaysOn
          placeholder={en.common.placeholder.searchByLastName}
        />,
        <LocalizedDateInput
          key={'GTE_submittedAt'}
          source='GTE_submittedAt'
          label={en.kyc.label.from}
          alwaysOn
        />,
        <LocalizedDateInput
          key={'LTE_submittedAt'}
          source='LTE_submittedAt'
          label={en.kyc.label.to}
          alwaysOn
        />,
        <SelectInput
          key={'status'}
          source='status'
          label={en.kyc.label.status}
          alwaysOn
          choices={[
            {
              id: 'SUBMITTED',
              name: `${en.kyc.label.submitted} 🟠`,
            },
            {
              id: 'ANALYSIS',
              name: `${en.kyc.label.analysis} 🟠`,
            },
            {
              id: 'APPROVED',
              name: `${en.kyc.label.approved} 🟢`,
            },
            {
              id: 'DENIED',
              name: `${en.kyc.label.denied} 🔴`,
            },
          ]}
        />,
      ]}
    >
      <Datagrid
        rowClick='show'
        bulkActionButtons={false}
        rowSx={(record) => {
          return {
            borderLeft:
              statusColors[
                (record?.document_review_status as DocumentSubmissionStatus) ??
                  'SUBMITTED'
              ],
          }
        }}
      >
        <TextField source='user_id' label={en.common.label.userId} />
        <TextField
          source='user_confirmed_values.first_name'
          label={en.kyc.label.firstName}
        />
        <TextField
          source='user_confirmed_values.last_name'
          label={en.kyc.label.lastName}
        />
        <TextField source='id_type' label={en.kyc.label.documentType} />
        <LocalizedDateField
          source='scanned_at'
          label={en.kyc.label.idSubmissionDate}
          showTime
        />
      </Datagrid>
    </List>
  )
}
