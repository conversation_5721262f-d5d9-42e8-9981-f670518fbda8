import { Box, Chip } from '@mui/material'
import {
  BooleanField,
  Datagrid,
  List,
  TextField,
  useRecordContext,
} from 'react-admin'
import { LocalizedDateField } from '../../../common/components/LocalizedDateField'

const TagsField = () => {
  const record = useRecordContext()
  return (
    <Box display='flex' gap={1}>
      {record?.permissions?.map((item: string, index: number) => (
        <Chip key={item + index} label={item} size='small' />
      ))}
    </Box>
  )
}

export const OperatorsList = () => {
  return (
    <List
      perPage={25}
      queryOptions={{
        refetchInterval: 20_000,
      }}
    >
      <Datagrid rowClick='show' bulkActionButtons={false}>
        <TextField source='user_id' />
        <TextField source='name' />
        <TextField source='email' />
        <TextField source='current_status' />
        <BooleanField source='is_logged_in' />
        <LocalizedDateField source='face_scan_last_liveness_check' showTime />
        <LocalizedDateField source='register_time' showTime />
        <TagsField />
      </Datagrid>
    </List>
  )
}
