import SendIcon from '@mui/icons-material/Send'
import { Button, useCreate, useNotify, useRecordContext } from 'react-admin'
import { autoHideDuration } from '../../../common/utils/consts'
import { en } from '../../../i18n/en'

/**
 * Button component that allows resending enrollment emails to operators
 */
export const ResendEnrollEmailButton = ({ resource }: { resource: string }) => {
  const record = useRecordContext()
  const notify = useNotify()

  const [create, { isPending }] = useCreate(
    resource,
    {
      data: {
        // On purpose empty string, for backend support!
        id: '',
        email: record?.email,
      },
    },
    {
      onSuccess: () => {
        notify('Successfully resent enroll email', {
          type: 'success',
          autoHideDuration,
        })
      },
      onError: (error) => {
        notify(`${en.common.error.genericError}: ${error}`, {
          type: 'error',
          autoHideDuration,
        })
      },
    }
  )

  return (
    <Button
      onClick={() =>
        create(resource, {
          data: {
            id: '',
            email: record?.email,
          },
        })
      }
      label={en.operator.label.resendEmail}
      loading={isPending}
      variant='contained'
    >
      <SendIcon />
    </Button>
  )
}
