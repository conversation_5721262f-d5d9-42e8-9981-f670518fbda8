import {
  FaceScanBiometrics,
  SDKConfig,
} from '../../../facetec-web-sdk/FaceBiometrics'
import { envs } from '../../common/utils/envs'
import { resources } from './resources'
import { AuthData, MappedAuthData } from './types/Auth.types'
import { FaceScanError, FaceScanParams } from './types/BiometricsApi.types'
import { FACETEC_INIT_KEY } from './utils/consts'
import { removeIdentity, setIdentity } from './utils/utils'

const { sessionToken, faceAuth, faceEnroll } = resources

const initSDK = async (sdkConfig: SDKConfig) => {
  return new Promise((resolve, reject) => {
    const isInitializedAlready = sessionStorage?.getItem(FACETEC_INIT_KEY)
    const onSdkInitialized = (isInit: boolean) => {
      if (isInitializedAlready) {
        resolve(Boolean(isInitializedAlready))
      } else {
        if (isInit) {
          sessionStorage.setItem(FACETEC_INIT_KEY, isInit.toString())
          resolve(isInit)
        } else {
          reject(
            new FaceScanError({
              error: `Failed to initialize SDK, might be server issue or browser issue preventing an init`,
            })
          )
        }
      }
    }

    FaceScanBiometrics.initializeSDK(sdkConfig, onSdkInitialized)
  })
}

const initSdkAndStartFaceScan = async ({
  sdkConfig,
  email,
  scanType,
  endpoints,
  onEnrollmentOnlyDone,
  enrollToken,
}: FaceScanParams): Promise<MappedAuthData> => {
  const isSDKInitialized = await initSDK(sdkConfig)

  return new Promise((resolve, reject) => {
    if (!isSDKInitialized) {
      reject(
        new FaceScanError({
          errorData: isSDKInitialized as string,
          message: `Failed to initialize SDK, might be server issue or browser issue preventing an init`,
        })
      )
    }

    FaceScanBiometrics.startScan({
      enrollToken,
      // Scan config
      baseUrl: new URL(`${envs.baseUrl}/`),
      endpoint: endpoints ?? {
        sessionToken,
        faceAuth,
        faceEnroll,
        idScan: '',
      },
      // User passed in params
      email,
      scanType,
      onComplete: (data) => {
        if (!data.error) {
          resolve(data as unknown as MappedAuthData)
        } else {
          reject(data.error)
        }
      },
      onEnrollmentOnlyDone: (data) => {
        onEnrollmentOnlyDone?.(data)
      },
    })
  })
}

const mockDevLogin = async (email: string) => {
  const request = new Request(`${envs.baseUrl}/${resources.mockLogin}`, {
    method: 'POST',
    body: JSON.stringify({ email }),
    headers: new Headers({ 'Content-Type': 'application/json' }),
  })
  let response: Response
  try {
    response = await fetch(request)
  } catch (_error) {
    throw new Error('Network error')
  }
  if (response.status < 200 || response.status >= 300) {
    throw new Error(response.statusText)
  }
  const auth = (await response.json()) as AuthData
  setIdentity(auth)
}

const logoutFromServer = async (authToken: string) => {
  try {
    await fetch(`${envs.baseUrl}/${resources.logout}`, {
      method: 'POST',
      headers: new Headers({
        'Content-Type': 'application/json',
        'X-Auth-Token': authToken,
      }),
    })
  } catch (_error) {
    throw new Error('Something went wrong')
  } finally {
    // Log the user out anyway
    removeIdentity()
  }
}

export { mockDevLogin, initSdkAndStartFaceScan, logoutFromServer }
