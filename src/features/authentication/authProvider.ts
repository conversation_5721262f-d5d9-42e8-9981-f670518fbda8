import { envs } from '../../common/utils/envs'
import { en } from '../../i18n/en'
import { initSdkAndStartFaceScan, logoutFromServer, mockDevLogin } from './Api'
import { ExtendedAuthProvider } from './types/AuthProvider.types'
import { facetecSdkConfig } from './utils/consts'
import { getIdentity, removeIdentity, setIdentity } from './utils/utils'

export const authProvider: ExtendedAuthProvider = {
  // called when the user attempts to log in
  async login({ email }: { email: string }) {
    if (envs.buildEnv === 'dev') {
      await mockDevLogin(email)
      return
    }

    const response = await initSdkAndStartFaceScan({
      email,
      scanType: 'AUTHENTICATION',
      sdkConfig: facetecSdkConfig,
    })

    if (response?.authTokenInfo?.authToken) {
      const mappedResponse = {
        auth_token_info: response.authTokenInfo,
        enrollment_complete: response.enrollmentCompleted,
        face_scan_reference_id: '',
        scan_result_blob: '',
      }
      setIdentity(mappedResponse)
      return mappedResponse
    }
  },
  // called when the user clicks on the logout button
  async logout() {
    const identity = getIdentity()
    if (identity) {
      await logoutFromServer(identity?.auth_token_info?.authToken)
      return
    }
  },
  // called when the API returns an error
  async checkError({ status }: { status: number }) {
    if (status === 401) {
      removeIdentity()
      throw new Error('Session expired')
    }
  },
  // called when the user navigates to a new location, to check for authentication
  async checkAuth() {
    if (!getIdentity()) {
      throw new Error('Authentication required')
    }
  },

  async canAccess() {
    //TODO: Permissions will be handled here when backend sorts out permissions
    // const permissions = getIdentity()?.auth_token_info?.permissionFlags
    // resource is name of the resource for example "users"
    return true
  },

  async getPermissions() {
    const permissions = getIdentity()?.auth_token_info?.permissionFlags
    return permissions
  },

  async signUp({ enrollToken }) {
    const response = await initSdkAndStartFaceScan({
      email: '',
      enrollToken,
      scanType: 'ENROLLMENT',
      sdkConfig: facetecSdkConfig,
    })

    if (response?.authTokenInfo?.authToken) {
      setIdentity({
        auth_token_info: response.authTokenInfo,
        enrollment_complete: response.enrollmentCompleted,
        face_scan_reference_id: '',
        scan_result_blob: '',
      })
      return { redirectTo: '/' }
    }

    throw new Error(en.authentication.error.genericFaceScanError)
  },

  supportAbortSignal: true,
}
