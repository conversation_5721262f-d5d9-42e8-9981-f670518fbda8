import { AuthData } from '../types/Auth.types'

const identityKey = 'authTokenStorage'

const storage = sessionStorage

const setIdentity = (identity: AuthData) => {
  if (!identity) {
    throw new TypeError('No auth token provided')
  }
  storage?.setItem(identityKey, JSON.stringify(identity))
}

const getIdentity = (): AuthData | null => {
  const storedAuthToken = storage?.getItem(identityKey)

  if (!storedAuthToken) {
    return null
  }

  try {
    return JSON.parse(storedAuthToken)
  } catch (error) {
    throw new Error(
      `Failed to parse JSON: ${(error as { message: string })?.message}`
    )
  }
}

const removeIdentity = () => storage?.removeItem(identityKey)

export { getIdentity, removeIdentity, setIdentity }
