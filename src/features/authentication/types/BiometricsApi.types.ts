import {
  FaceScanData,
  ScanType,
  SDKConfig,
} from '../../../../facetec-web-sdk/FaceBiometrics'

export class FaceScanError extends Error {
  error: unknown
  errorData: string | undefined
  constructor({
    message,
    error,
    errorData,
  }: {
    message?: string
    error?: unknown
    errorData?: string
  }) {
    super(message)
    this.name = 'FaceScanError'
    this.error = error
    this.errorData = errorData
  }
}

type FaceScanParams = {
  sdkConfig: SDKConfig
  email: string
  scanType: ScanType
  endpoints?: {
    sessionToken: string
    faceAuth: string
    faceEnroll: string
    idScan: string
  }
  onEnrollmentOnlyDone?: (data: FaceScanData) => void
  enrollToken?: string
}

export type { FaceScanParams }
