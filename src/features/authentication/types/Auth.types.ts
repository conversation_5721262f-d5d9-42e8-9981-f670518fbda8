type OperatorPermissions = 'verify_Address' | 'create_Operator' | 'create_Admin'

type AuthTokenInfo = {
  authToken: string
  permissionFlags: Array<OperatorPermissions>
  refreshToken: string
  remainingTime: number
}

type AuthData = {
  auth_token_info: AuthTokenInfo
  enrollment_complete: boolean
  face_scan_reference_id: string
  scan_result_blob: string
}

type MappedAuthData = {
  authTokenInfo: AuthData['auth_token_info']
  error: object | null
  enrollmentCompleted: boolean
  idScanCompleted: boolean
  userAccountInfo?: object
}

export type { AuthData, MappedAuthData }
