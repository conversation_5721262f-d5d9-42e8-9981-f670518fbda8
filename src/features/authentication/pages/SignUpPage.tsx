import { Stack, Typography } from '@mui/material'
import { useEffect } from 'react'
import { Button, useNotify, useRedirect } from 'react-admin'
import { en } from '../../../i18n/en'
import { authProvider } from '../authProvider'

/**
 * React admin does not support sign up by default, this is a fully custom
 * component that allows for users to sign up to our internal dashboard
 */
export const SignUpPage = ({ enrollToken }: { enrollToken: string }) => {
  const notify = useNotify()
  const redirect = useRedirect()

  const startScan = async () => {
    try {
      const response = await authProvider.signUp({
        enrollToken,
      })
      if (response?.redirectTo) {
        redirect(response.redirectTo)
      }
    } catch (error) {
      notify(
        `${en.common.error.genericError}: ${(error as { message: string })?.message}`,
        {
          type: 'error',
        }
      )
    }
  }

  // biome-ignore lint/correctness/useExhaustiveDependencies: no callback wrap
  useEffect(() => {
    if (enrollToken) {
      startScan()
    }
  }, [enrollToken])

  return (
    <Stack
      spacing={5}
      justifyContent={'center'}
      alignItems={'center'}
      padding={3}
      maxWidth={400}
    >
      <Typography margin={0}>
        {en.authentication.message.noAutoFaceScan}
      </Typography>

      <Button variant='contained' color='primary' fullWidth onClick={startScan}>
        {en.authentication.button.bioEnroll}
      </Button>
    </Stack>
  )
}
