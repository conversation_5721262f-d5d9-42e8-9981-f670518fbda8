import { Login, LoginForm, TextInput } from 'react-admin'
import { useSearchParams } from 'react-router'
import { tontineEmailValidator } from '../../../common/utils/extendedValidators'
import { en } from '../../../i18n/en'
import { loginPageBgImg } from '../utils/consts'
import { SignUpPage } from './SignUpPage'

/**
 * Extended login page to support sign up as well if `enrollToken` q param is
 * provided
 */
export const LoginPage = () => {
  const [searchParams] = useSearchParams()
  const enrollToken = searchParams.get('enrollToken') ?? ''

  return (
    <Login backgroundImage={loginPageBgImg}>
      {enrollToken ? (
        <SignUpPage enrollToken={enrollToken} />
      ) : (
        <LoginForm>
          <TextInput
            source='email'
            label={en.authentication.label.tontineEmail}
            type='email'
            validate={tontineEmailValidator}
          />
        </LoginForm>
      )}
    </Login>
  )
}
