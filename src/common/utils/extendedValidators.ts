import { email, required } from 'react-admin'
import { en } from '../../i18n/en'

const validDomains = ['tontine.ie', 'tontine.com']

const domainValidator = (email: string) => {
  const [, domain] = email.split('@')
  if (!validDomains.includes(domain)) {
    return `Only ${validDomains.join(', ')} email domains are supported`
  }
}

const requireReasonForType = (
  _: unknown,
  allValues: { reasonType: string }
) => {
  if (!allValues.reasonType) return undefined
  // Add logic here to check if the selected reasonType requires a reason
  // For example, if certain reasonTypes require explanation
  if (allValues.reasonType === 'OTHER_REASON') {
    return en.kyc.label.denyReasonRequired
  }
  return undefined
}

const denialReasonValidator = [required(), requireReasonForType]
const tontineEmailValidator = [required(), email(), domainValidator]

export { tontineEmailValidator, denialReasonValidator }
