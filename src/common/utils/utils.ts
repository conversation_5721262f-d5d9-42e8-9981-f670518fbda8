import { Identifier, RaR<PERSON><PERSON> } from 'react-admin'

function getNestedValue<T = unknown>({
  record,
  path,
}: {
  record?: RaRecord<Identifier>
  path: string
}): T | undefined {
  const keys = path.split('.')
  let current: unknown = record

  for (const key of keys) {
    if (current === null || current === undefined) return undefined
    current = (current as RaRecord<Identifier>)[key]
  }
  return current as T | undefined
}

export { getNestedValue }
