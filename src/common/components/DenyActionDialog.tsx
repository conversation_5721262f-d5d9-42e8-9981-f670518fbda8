import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos'
import DoDisturbIcon from '@mui/icons-material/DoDisturb'
import { Dialog, Stack } from '@mui/material'
import { useState } from 'react'
import {
  Button,
  RadioButtonGroupInput,
  required,
  SimpleForm,
  TextInput,
  useNotify,
  useRecordContext,
  useRedirect,
  useUpdate,
} from 'react-admin'
import { DenyActionDialogProps } from '../../features/kyc/types/DenyActionDialog.types'
import { en } from '../../i18n/en'
import { autoHideDuration } from '../utils/consts'
import { denialReasonValidator } from '../utils/extendedValidators'
import { getNestedValue } from '../utils/utils'

/**
 * Used with "deny" type of APIs, the id is appended to the request's data
 * automatically.
 */
export const DenyActionDialog = ({
  redirectTo,
  resource,
  deniedActionMsg,
  denyReasons,
  recordKey,
}: DenyActionDialogProps) => {
  const [openDialog, setOpenDialog] = useState(false)
  const record = useRecordContext()
  const notify = useNotify()
  const redirect = useRedirect()
  const [selectedReasonType, setSelectedReasonType] = useState<string>()

  const [deny, { isPending }] = useUpdate(undefined, undefined, {
    onSuccess: () => {
      notify(deniedActionMsg ?? en.common.feedback.documentDenied, {
        type: 'success',
        autoHideDuration,
      })
      setOpenDialog(false)
      redirect(redirectTo ?? '/')
    },
    onError: (error) => {
      notify(`${en.common.error.unknownError}: ${error}`, {
        type: 'error',
        autoHideDuration,
      })
    },
  })

  const onSubmit = ({
    reasonType,
    reason,
  }: {
    reasonType?: string
    reason?: string
  }) =>
    deny(resource, {
      // On purpose empty string, for backend support!
      id: '',
      data: {
        id: getNestedValue({ record, path: recordKey }),
        reason_type: reasonType,
        reason,
      },
    })

  return (
    <>
      <Button
        label={en.common.button.denyDocument}
        onClick={() => setOpenDialog(true)}
        color='error'
      >
        <DoDisturbIcon />
      </Button>
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <SimpleForm id='deny_form' toolbar={false} onSubmit={onSubmit}>
          <RadioButtonGroupInput
            source='reasonType'
            validate={[required()]}
            choices={denyReasons}
            onChange={(e) => setSelectedReasonType(e.target.value)}
          />
          {selectedReasonType === 'OTHER_REASON' && (
            <TextInput
              spellCheck
              source='reason'
              label={en.kyc.label.denyTextReason}
              fullWidth
              multiline
              validate={denialReasonValidator}
            />
          )}
          <Stack
            direction='row'
            spacing={5}
            width={'100%'}
            justifyContent={'flex-end'}
          >
            <Button
              onClick={() => setOpenDialog(false)}
              label={en.common.button.closeAction}
              disabled={isPending}
            >
              <ArrowBackIosIcon />
            </Button>
            <Button
              form='deny_form'
              type='submit'
              label={en.common.button.denyDocument}
              disabled={isPending}
              variant='contained'
              color='error'
            >
              <DoDisturbIcon />
            </Button>
          </Stack>
        </SimpleForm>
      </Dialog>
    </>
  )
}
