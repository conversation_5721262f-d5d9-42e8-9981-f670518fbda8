import CancelIcon from '@mui/icons-material/Cancel'
import CheckCircle from '@mui/icons-material/CheckCircle'
import { Stack, Typography } from '@mui/material'
import { FunctionField } from 'react-admin'
import { KYCStatusFieldProps } from '../types/KYCStatusField.types'

export const KYCStatusField = ({ level, label }: KYCStatusFieldProps) => {
  return (
    <FunctionField
      label={label}
      source='kyc_status'
      render={(record) => (
        <Stack spacing={1}>
          {Object.entries(record.kyc_status?.[level]?.requirements ?? {}).map(
            ([key, value]) => (
              <Stack
                key={key}
                direction='row'
                alignItems='center'
                justifyContent='space-between'
                spacing={1.5}
              >
                <Typography variant='body2'>{key}</Typography>
                {value ? (
                  <CheckCircle fontSize='small' color='success' />
                ) : (
                  <CancelIcon fontSize='small' color='error' />
                )}
              </Stack>
            )
          )}
        </Stack>
      )}
    />
  )
}
