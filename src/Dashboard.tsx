import AssignmentIndIcon from '@mui/icons-material/AssignmentInd'
import MapIcon from '@mui/icons-material/Map'
import PeopleIcon from '@mui/icons-material/People'
import SupervisedUserCircle from '@mui/icons-material/SupervisedUserCircle'
import { Box, Button, Card, CardContent, Grid, Typography } from '@mui/material'
import { Title } from 'react-admin'
import { useNavigate } from 'react-router-dom'
import { resources as kycResources } from './features/kyc/resources'
import { resources as tontineUserResources } from './features/mt-user-management/resources'
import { resources as operatorResources } from './features/operator/resources'
import { en } from './i18n/en'

export const Dashboard = () => {
  const navigate = useNavigate()

  const dashboardCards = [
    {
      title: en.dashboard.feature.tontineUsers.title,
      subtitle: en.dashboard.feature.tontineUsers.subtitle,
      icon: <SupervisedUserCircle sx={{ fontSize: 48, color: '#2975BF' }} />,
      buttonText: en.common.button.officeNavigation,
      buttonColor: '#2975BF',
      path: `/${tontineUserResources.tontineUsersAll}`,
    },
    {
      title: en.dashboard.feature.idDocumentReviewQueue.title,
      subtitle: en.dashboard.feature.idDocumentReviewQueue.subtitle,
      icon: <AssignmentIndIcon sx={{ fontSize: 48, color: '#32b443ff' }} />,
      buttonText: en.common.button.officeNavigation,
      buttonColor: '#32b443ff',
      path: `/${kycResources.idDocumentListAll}`,
    },
    {
      title: en.dashboard.feature.addressReviewQueue.title,
      subtitle: en.dashboard.feature.addressReviewQueue.subtitle,
      icon: <MapIcon sx={{ fontSize: 48, color: '#22B573' }} />,
      buttonText: en.common.button.officeNavigation,
      buttonColor: '#22B573',
      path: `/${kycResources.addressReviewListAll}`,
    },
    {
      title: en.dashboard.feature.companyOperators.title,
      subtitle: en.dashboard.feature.companyOperators.subtitle,
      icon: <PeopleIcon sx={{ fontSize: 48, color: '#9900FF' }} />,
      buttonText: en.common.button.officeNavigation,
      buttonColor: '#9900FF',
      path: `/${operatorResources.operatorsListAll}`,
    },
  ]

  return (
    <>
      <Title title={en.dashboard.topBarTitle} />
      <Box display={'flex'} flexDirection={'column'} gap={5} padding={4}>
        <Box>
          <Typography variant='h5' component='h2'>
            {en.dashboard.title}
          </Typography>
          <Typography variant='body1'>
            {en.dashboard.subtitle}
            <Typography
              variant='body1'
              component='a'
              href='https://3.basecamp.com/5235135/buckets/24897711/chats/4334810753'
              sx={{
                color: 'primary.main',
                textDecoration: 'underline',
                cursor: 'pointer',
                '&:hover': {
                  opacity: 0.8,
                },
              }}
            >
              {' '}
              {en.dashboard.supportLink}
            </Typography>
          </Typography>
        </Box>
        <Grid container spacing={3}>
          {dashboardCards.map((card, index) => (
            <Card
              key={index}
              sx={{ textAlign: 'center', padding: 1, minWidth: '16rem' }}
            >
              <CardContent>
                <Box mb={2}>{card.icon}</Box>
                <Typography variant='h6' component='h3'>
                  {card.title}
                </Typography>
                <Typography
                  variant='body2'
                  color='text.secondary'
                  maxWidth={300}
                  mb={3}
                >
                  {card.subtitle}
                </Typography>
                <Button
                  variant='contained'
                  fullWidth
                  sx={{
                    backgroundColor: card.buttonColor,
                    '&:hover': {
                      backgroundColor: card.buttonColor,
                      opacity: 0.8,
                    },
                    borderRadius: '.5rem',
                    textTransform: 'none',
                    fontWeight: '800',
                  }}
                  onClick={() => navigate(card.path)}
                >
                  <Typography variant='subtitle1' align='center'>
                    {card.buttonText}
                  </Typography>
                </Button>
              </CardContent>
            </Card>
          ))}
        </Grid>
      </Box>
    </>
  )
}
